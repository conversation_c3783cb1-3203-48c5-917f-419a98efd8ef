import { showToast } from "@/lib/toast";
import { fetchHospitalsAPI } from "@/network/hospitals/get";
import { useEffect, useState } from "react";

const useHospitals  = () => {
    const [loading, setLoading] = useState(false);
    
    const fetchHospitals = () => {
        try {
            setLoading(true);
            const response = fetchHospitalsAPI();
            
        } catch (err) {
            showToast({
                type: "error",
                message: "Error Checking..."
            })
            console.log(err, 'errorChecking...')
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchHospitals();
    },[])

    return {
        loading,

    }
}

export default useHospitals;