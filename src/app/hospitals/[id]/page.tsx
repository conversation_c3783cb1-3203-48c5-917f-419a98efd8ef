'use client'
import React from 'react';
import CustomHead from "@/components/CustomHead";
import { Input } from "@/components/ui/input";
import DoctorCard from "@/components/DoctorCard";
import DownloadApp from "@/components/DownloadApp";
import HospitalDetails from "@/components/HospitalDetails";
import Lister from "@/components/Lister";
import SearchFilters from "@/components/SearchFilters";
import { hospitalDoctorFilters } from "@/dummy/filters";
import { useRouter, useParams } from "next/navigation";
import useDoctorListing from "@/hooks/doctors/use-hook";
import useHospitalDetail from "@/hooks/hospitals/use-detail-hook";
import { DoctorI } from "@/network/doctors/types";
import Search from "~/svg/search.svg";
import Image from 'next/image';

const HospitalDetailed = () => {
    const router = useRouter()
    const params = useParams()
    const hospitalId = params.id as string

    // Use the hospital detail hook to get hospital information
    const {
        hospital,
        loading: hospitalLoading,
        error: hospitalError
    } = useHospitalDetail(hospitalId);

    // Use the doctor listing hook for API data - filter by hospital_id
    const {
        doctors: apiDoctors,
        loading,
        loadingMore,
        hasMore,
        loadMore,
        search,
        filterByHospital,
        error
    } = useDoctorListing({
        initialLimit: 12,
        autoFetch: false // Don't auto-fetch, we'll fetch with hospital_id
    });

    // Fetch doctors for this specific hospital when component mounts
    React.useEffect(() => {
        if (hospitalId) {
            filterByHospital(hospitalId);
        }
    }, [hospitalId, filterByHospital]);

    const handleSearchChange = (query: string) => {
        search(query);
    };
    return (
        <div>
            <div className="flex flex-col items-center justify-center">
                <div className="space-y-10 mt-20">
                    <CustomHead text='Find Your Hospital' highlight='Hospital' />
                    <HospitalDetails hospital={hospital} loading={hospitalLoading} />
                    {hospitalError && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                            <p>Error loading hospital details: {hospitalError}</p>
                        </div>
                    )}
                    <h4 className="text-[#004746] font-medium text-4xl text-center">
                        Doctors From {hospital?.name || 'This Hospital'}
                    </h4>
                    <div className="flex flex-col items-center justify-center gap-10 mb-10">
                        <div className="flex w-full max-w-3xl rounded-full border shadow-xs overflow-hidden">
                            <div className="flex items-center px-4 py-3 flex-grow text-gray-500">
                                <Image src={Search} alt='Search' className='w-6 h-6 mr-2' />
                                <Input
                                    type="text"
                                    placeholder="Search doctors in this hospital"
                                    className="border-none p-0 focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none text-sm placeholder:text-gray-400"
                                    onChange={(e) => handleSearchChange(e.target.value)}
                                />
                            </div>
                        </div>
                        <SearchFilters filters={hospitalDoctorFilters} />
                    </div>
                </div>

            </div>

            {/* Error state */}
            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 max-w-7xl mx-auto">
                    <p>Error loading doctors: {error}</p>
                </div>
            )}

            <section className="max-w-7xl mx-auto px-4 mb-10">
                <Lister<DoctorI>
                    items={apiDoctors}
                    itemsPerPage={12}
                    hasMore={hasMore}
                    loading={loading}
                    loadingMore={loadingMore}
                    onLoadMore={loadMore}
                    useLegacyPagination={false}
                    renderItem={(doctor) => (
                        <DoctorCard
                            doctor={doctor}
                            onBook={() => router.push(`/doctors/${doctor.id}`)}
                        />
                    )}
                />
            </section>

            <DownloadApp />
        </div>
    )
}

export default HospitalDetailed;