{"name": "zodo-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.6", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.507.0", "next": "15.3.1", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.2.0", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}